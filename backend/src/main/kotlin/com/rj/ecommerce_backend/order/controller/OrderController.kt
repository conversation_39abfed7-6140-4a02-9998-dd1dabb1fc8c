// File: backend/src/main/kotlin/com/rj/ecommerce_backend/order/controller/OrderController.kt
package com.rj.ecommerce_backend.order.controller

import com.rj.ecommerce.api.shared.dto.order.OrderCreateRequestDTO
import com.rj.ecommerce.api.shared.dto.order.OrderDTO
import com.rj.ecommerce_backend.order.exception.OrderNotFoundException
import com.rj.ecommerce_backend.order.search.OrderSearchCriteria
import com.rj.ecommerce_backend.order.service.OrderCommandService
import com.rj.ecommerce_backend.order.service.OrderQueryService
import com.rj.ecommerce_backend.order.usecases.CreateOrderUseCase
import com.rj.ecommerce_backend.sorting.OrderSortField
import com.rj.ecommerce_backend.sorting.SortValidator
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.tags.Tag
import io.github.oshai.kotlinlogging.KotlinLogging
import jakarta.validation.Valid
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*

@RestController
@Tag(name = "Order", description = "APIs for customer order management")
@RequestMapping("/api/v1")
class OrderController(
    // CORRECT: Inject all three specialized components
    private val orderQueryService: OrderQueryService,
    private val orderCommandService: OrderCommandService,
    private val createOrderUseCase: CreateOrderUseCase,
    private val sortValidator: SortValidator
    // REMOVED: orderMapper dependency
) {
    companion object {
        val logger = KotlinLogging.logger {}
    }

    @GetMapping("/users/{userId}/orders")
    @PreAuthorize("#pathUserId == authentication.principal.id or hasRole('ADMIN')")
    fun getAllOrdersForUser(
        @PathVariable pathUserId: Long,
        criteriaInput: OrderSearchCriteria,
        @RequestParam(defaultValue = "0") page: Int,
        @RequestParam(defaultValue = "10") size: Int,
        @RequestParam(defaultValue = "id:asc") sort: String?
    ): ResponseEntity<Page<OrderDTO>> {
        // ... (logging and validation) ...
        val pageable = PageRequest.of(page, size, sortValidator.validateAndBuildSort(sort, OrderSortField::class.java))
        val serviceCriteria = criteriaInput.copy(userId = pathUserId)

        // CORRECT: Call the query service
        val ordersPage = orderQueryService.getOrdersForUser(pageable, serviceCriteria)

        logger.info { "Retrieved ${ordersPage.numberOfElements} orders for user ID $pathUserId on page $page." }
        return ResponseEntity.ok(ordersPage)
    }

    @GetMapping("/users/{userId}/orders/{orderId}")
    fun getOrder(
        @PathVariable userId: Long,
        @PathVariable orderId: Long
    ): ResponseEntity<OrderDTO> {
        logger.info { "Request to get order ID: $orderId for user ID: $userId" }

        // CORRECT: Call the query service
        val orderDto = orderQueryService.getOrderById(userId, orderId)
            ?: throw OrderNotFoundException(orderId)

        return ResponseEntity.ok(orderDto)
    }

    @PostMapping("/users/{userId}/orders")
    @PreAuthorize("#userId == authentication.principal.id")
    fun createOrder(
        @PathVariable userId: Long,
        @Valid @RequestBody orderCreateRequestDTO: OrderCreateRequestDTO
    ): ResponseEntity<OrderDTO> {
        logger.info { "Request to create order for user ID: $userId" }

        // CORRECT: Call the dedicated use case
        val orderDto = createOrderUseCase.execute(userId, orderCreateRequestDTO)

        logger.info { "Successfully created order ID: ${orderDto.id} for user ID: $userId" }
        return ResponseEntity.status(HttpStatus.CREATED).body(orderDto)
    }

    @DeleteMapping("/users/{userId}/orders/{orderId}")
    @PreAuthorize("#userId == authentication.principal.id or hasRole('ADMIN')")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun cancelOrder(
        @PathVariable userId: Long,
        @PathVariable orderId: Long
    ) {
        logger.info { "Request to cancel order ID: $orderId for user ID: $userId" }

        // CORRECT: Call the command service
        orderCommandService.cancelOrder(userId, orderId)

        logger.info { "Successfully processed cancellation for order ID: $orderId" }
    }
}